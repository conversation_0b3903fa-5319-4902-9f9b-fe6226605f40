export type BillingStatus = 'free' | 'basic' | 'free-ended';

export interface Project {
  project_id: string;
  user_id: string;
  created_at: string;
  name: string;
  billing_status?: BillingStatus;
  accounts: Array<{
    agentId: string | null;
    platform: string;
    username?: string;
    connected: boolean;
    organizationId?: string;
    organizationName?: string;
    hasOrganizationAccess?: boolean;
    hasPremium?: boolean;
    subscriptionType?: string;
    verifiedType?: string;
    isBlueVerified?: boolean;
  }>;
}

export interface ProjectFormValues {
  name: string;
  isFreeTrial?: boolean;
}

export interface ImageMetadata {
  id: string;
  project_id: string;
  agent_id: string;
  image_type: 'generated' | 'uploaded';
  file_path: string;
  file_name: string;
  plan_id?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}
