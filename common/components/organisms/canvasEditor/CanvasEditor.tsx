'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import * as fabric from 'fabric';
import {
  CanvasSidebar, CanvasToolbar,
} from '@/common/components/organisms';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);

  const calculateFitToViewZoom = useCallback((canvasWidth: number, canvasHeight: number) => {
    if (!containerRef.current) {
      return 1;
    }

    const container = containerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / canvasWidth;
    const scaleY = containerHeight / canvasHeight;

    return Math.min(scaleX, scaleY, 1.2);
  }, []);

  const zoomIn = () => {
    const newZoom = Math.min(zoomLevel * 1.2, 5);
    setZoomLevel(newZoom);
  };

  const zoomOut = () => {
    const newZoom = Math.max(zoomLevel / 1.2, 0.1);
    setZoomLevel(newZoom);
  };

  const fitToView = useCallback(() => {
    if (!fabricCanvas || !containerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const newZoom = calculateFitToViewZoom(canvasSize.width, canvasSize.height);

    setZoomLevel(newZoom);
    setTimeout(() => {
      if (containerRef.current) {
        const container = containerRef.current;
        const scrollableWidth = container.scrollWidth;
        const scrollableHeight = container.scrollHeight;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        const centerScrollLeft = (scrollableWidth - containerWidth) / 2;
        const centerScrollTop = (scrollableHeight - containerHeight) / 2;

        container.scrollTo({
          left: centerScrollLeft,
          top: centerScrollTop,
          behavior: 'smooth',
        });
      }
    }, 50);
  }, [fabricCanvas, platform, calculateFitToViewZoom, setZoomLevel]);

  
  useEffect(() => {
    if (fabricCanvas) {
      fabricCanvas.setZoom(zoomLevel);
      fabricCanvas.renderAll();
    }
  }, [zoomLevel, fabricCanvas]);

  useEffect(() => {
    if (fabricCanvas && isOpen) {
      setTimeout(() => {
        fitToView();
      }, 100);
    }
  }, [fabricCanvas, isOpen, fitToView]);

  useEffect(() => {
    if (!canvasRef.current || !isOpen) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    setFabricCanvas(canvas);

    canvas.on('mouse:wheel', function (opt) {
      if (opt.e.ctrlKey || opt.e.metaKey) {
        const delta = opt.e.deltaY;
        let zoom = canvas.getZoom();
        zoom *= 0.999 ** delta;
        if (zoom > 5) {
          zoom = 5;
        }
        if (zoom < 0.1) {
          zoom = 0.1;
        }
        canvas.zoomToPoint(new fabric.Point(opt.e.offsetX, opt.e.offsetY), zoom);
        setZoomLevel(zoom);
        opt.e.preventDefault();
        opt.e.stopPropagation();
      }
    });

    canvas.on('mouse:down', function (this: any, opt: any) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function (this: any, opt: any) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function (this: any) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    canvas.on('mouse:dblclick', function (opt: any) {
      const target = opt.target;
      if (target && (target.type === 'text' || target.type === 'i-text')) {
        const textObject = target as fabric.IText;
        canvas.setActiveObject(textObject);
        textObject.enterEditing();
        textObject.selectAll();
        canvas.renderAll();
      }
    });

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach(obj => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    if (initialImage) {
      fabric.FabricImage.fromURL(initialImage).then((img: any) => {
        const canvasWidth = canvas.width!;
        const canvasHeight = canvas.height!;
        const imgWidth = img.width!;
        const imgHeight = img.height!;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        img.scale(scale);
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2,
        });
        canvas.add(img);
        canvas.renderAll();
      });
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage, platform]);

  const handleSaveDesign = () => {
    if (!fabricCanvas) {
      console.error('Canvas not available');
      return;
    }

    try {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      });

      fetch(dataURL)
        .then(res => res.blob())
        .then(blob => {
          const timestamp = Date.now();
          const fileName = `canvas-design-${timestamp}.png`;
          const file = new File([blob], fileName, { type: 'image/png' });
          const tempUrl = URL.createObjectURL(file);
          onSave(tempUrl);
        })
        .catch(error => {
          console.error('Error converting canvas to file:', error);
          onSave('');
        });
    } catch (error) {
      console.error('Error generating canvas image:', error);
      onSave('');
    }
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-[60px] left-0 right-0 bottom-0 h-[calc(100vh-60px)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasToolbar
        onClose={onClose}
        onSaveDesign={handleSaveDesign}
        canvas={fabricCanvas}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        onFitToView={fitToView}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={fabricCanvas}
            agentId={agentId}
            planId={planId}
          />
        </div>
        <div className="flex-1 bg-neutral-800 flex flex-col overflow-hidden">
          <div ref={containerRef} className="flex-1 w-full overflow-auto scroll-smooth">
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `${Math.max(100, zoomLevel * 120)}%`,
                minWidth: `${Math.max(100, zoomLevel * 120)}%`,
                padding: `${Math.max(32, zoomLevel * 40)}px`,
              }}
            >
              <div
                className="bg-violets-are-blue/5 rounded-xl p-2 md:p-4 w-auto shadow-lg transition-transform duration-200"
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center center',
                }}
              >
                <canvas
                  ref={canvasRef}
                  className="border border-gray-200 rounded block"
                />
              </div>
            </div>
          </div>
          <div className="p-2 md:p-4 text-center text-gray-500 text-xs">
            <div className="flex flex-col items-center gap-1">
              <p>Ctrl + mouse wheel to zoom | Alt + drag to pan | Double-click text to edit inline | Delete key to remove selected objects</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
