import {
  useEffect, useMemo, useRef, forwardRef,
} from "react";
import autosize from 'autosize';
import { TextAreaProps } from "./types"

const getErrorClasses = (error: boolean | undefined) => error ? '!border-tulip pr-10' : '';

export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>((props, ref) => {
  const {
    name = 'input-name',
    id = 'input-id',
    placeholder = 'input placeholder',
    value = '',
    disabled = false,
    maxHeight = '220px',
    error = false,
    errorMessage = '',
    labelText = '',
    width = 'w-60',
    onBlur = () => null,
    onChange = () => null,
    ...rest
  } = props;
  const localRef = useRef<HTMLTextAreaElement>(null);
  const textAreaRef = ref || localRef;
  useEffect(() => {
    if (textAreaRef && 'current' in textAreaRef && textAreaRef.current) {
      autosize(textAreaRef.current); 
    }
    return () => {
      if (textAreaRef && 'current' in textAreaRef && textAreaRef.current) {
        autosize.destroy(textAreaRef.current);
      }
    };
  }, [textAreaRef]);

  useEffect(() => {
    if (textAreaRef && 'current' in textAreaRef && textAreaRef.current) {
      autosize.update(textAreaRef.current);
    }
  }, [value, textAreaRef]); 
  const computedClasses = useMemo(() => {
    return getErrorClasses(error);
  }, [error]);
  return (
    <div className={width}>
      <div className={`flex`}>
        {labelText && (
          <label htmlFor={id} className={`block text-sm font-medium text-white`}>
            {labelText}
          </label>
        )}
      </div>
      <div className="mt-2 relative">
        <div className={`w-full disabled:bg-white/15 disabled:border-white/50 bg-white/5 backdrop-blur-sm border border-white/5 hover:border-violets-are-blue focus:border-violets-are-blue p-2 rounded-2xl ${computedClasses}`}>
          <div style={{ maxHeight: maxHeight }} className={`overflow-auto`}>
            <textarea
              ref={textAreaRef}
              name={name}
              id={id}
              disabled={disabled}
              value={value}
              onChange={onChange}
              onBlur={onBlur}
              placeholder={placeholder}
              {...rest}
              className={`w-full text-white disabled:text-neutral-200 font-normal disabled:cursor-not-allowed bg-transparent placeholder:text-gray-600 text-sm py-0 pl-2 pr-1 focus:!outline-none resize-none`}
            />
          </div>
        </div>
      </div>
      {error && (
        <p className="mt-0.5 text-sm text-tulip" data-cy={`${name}-error`}>
          {errorMessage}
        </p>
      )}
    </div>
  )
})
TextArea.displayName = 'TextArea';