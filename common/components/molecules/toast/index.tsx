'use client';
import { Transition } from "@headlessui/react";
import {
  Toaster as ReactToaster, Toast, ToastIcon, resolveValue,
} from "react-hot-toast";
import { cn } from "@/common/utils/helpers";
import { toasterConfig } from "./config";
import { secondaryFont } from "@/common/utils/localFont";

export const Toaster = () => {
  return (
    <ReactToaster
      {...toasterConfig}
      position="bottom-right"
    >
      {(toast: Toast) => {
        return (
          <Transition
            appear
            show={toast.visible}
            className={cn(
              'transform py-3 px-4 rounded-xl flex items-center text-2xl bg-violets-are-blue/5 border border-white/5',
            )}
            enter="transition-all duration-150"
            enterFrom="opacity-0 scale-50"
            enterTo="opacity-100 scale-100"
            leave="transition-all duration-150"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-75"
          >
            <ToastIcon toast={toast} />
            <p
              className={cn(
                toast.type === 'success' ? 'text-green-300/50' : 'text-tulip/50',
                'text-xs font-medium', `${secondaryFont.className}`,
              )}
            >
              {resolveValue(toast.message, toast)}
            </p>
          </Transition>
        )
      }}
    </ReactToaster>
  );
};
