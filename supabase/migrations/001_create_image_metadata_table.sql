-- Create table for image metadata tracking
CREATE TABLE IF NOT EXISTS image_metadata (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "project_id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "image_type" TEXT NOT NULL CHECK (image_type IN ('generated', 'uploaded')),
    "file_path" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "plan_id" TEXT,
    "description" TEXT,
    "created_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_image_metadata_project_id ON image_metadata("project_id");
CREATE INDEX IF NOT EXISTS idx_image_metadata_agent_id ON image_metadata("agent_id");
CREATE INDEX IF NOT EXISTS idx_image_metadata_type ON image_metadata("image_type");
CREATE INDEX IF NOT EXISTS idx_image_metadata_created_at ON image_metadata("created_at");
CREATE INDEX IF NOT EXISTS idx_image_metadata_project_type ON image_metadata("project_id", "image_type");

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_image_metadata_updated_at ON image_metadata;
CREATE TRIGGER update_image_metadata_updated_at 
    BEFORE UPDATE ON image_metadata 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE image_metadata ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to access their own project images
CREATE POLICY "Users can access their own project images" ON image_metadata
    FOR ALL USING (
        project_id IN (
            SELECT project_id FROM projects WHERE user_id = auth.uid()
        )
    );
