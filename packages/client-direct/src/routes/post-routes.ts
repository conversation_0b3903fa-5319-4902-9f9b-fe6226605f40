import express from "express";
import {
    AgentRuntime,
    composeContext,
    generateText,
    ModelClass,
    elizaLogger,
} from "@elizaos/core";
import Anthropic from "@anthropic-ai/sdk";
import { upload } from "../utils/upload-config";
import * as fs from "fs";
import * as path from "path";
import { CustomRequest } from "../models/types";
import {
    copyImprovementTemplate,
    copyImprovementTwitterTemplate,
    linkedInPostTemplate,
    linkedInPostWithoutKnowledgeBaseTemplate,
    twitterPostTemplate,
    twitterPostWithoutKnowledgeBaseTemplate,
    twitterPostPremiumTemplate,
    twitterPostPremiumWithoutKnowledgeBaseTemplate,
} from "../templates/post-template";

// Import the ScheduledPost interface
interface ScheduledPost {
    id: string;
    content: string;
    scheduledTime: Date;
    status: "draft" | "approved" | "rejected" | "posted";
    topics?: string[];
    notes?: string;
    reviewFeedback?: string;
    attachments?: string[];
    localPath?: Array<{
        type: string;
        url: string;
    }>;
}

/**
 * Performs web search using Anthropic SDK to gather additional context for content generation
 */
async function performWebSearchForContent(
    systemPrompt: string,
    agent: AgentRuntime
): Promise<string> {
    try {
        const apiKey = agent.getSetting("ANTHROPIC_API_KEY");
        if (!apiKey) {
            elizaLogger.debug(
                "No Anthropic API key found, skipping web search"
            );
            return systemPrompt;
        }

        elizaLogger.debug("Performing web search for content generation");

        const directAnthropic = new Anthropic({
            apiKey: apiKey,
            fetch: agent.fetch,
        });

        // Use streaming to avoid timeout issues for long operations
        const stream = await directAnthropic.messages.create({
            model: "claude-sonnet-4-20250514",
            max_tokens: 4000,
            temperature: 0.7,
            system: `You are a professional social media content strategist with access to web search capabilities.

## WEB SEARCH CAPABILITIES
You have access to web search to find current information, trending topics, and real-time data. Use this to:
- Find current industry trends and professional insights
- Get up-to-date business statistics and market data
- Discover recent developments in technology and business
- Find current professional events or news to reference
- Research competitor activities and market positioning
- Identify trending hashtags and social media conversations

Use web search strategically to enhance content with current, relevant information that will make social media posts more engaging and timely.`,
            tools: [
                {
                    type: "web_search_20250305",
                    name: "web_search",
                    max_uses: 3,
                } as any, // Type assertion for web search tool format
            ],
            messages: [
                {
                    role: "user",
                    content: `I need to create social media content about: ${systemPrompt}. Please search for current, relevant information that would help create engaging and informative content about this topic. Focus on finding recent trends, statistics, news, or developments that would make the content more compelling and timely.`,
                },
            ],
            stream: true,
        });

        // Collect the streamed response
        let webSearchContext = "";
        for await (const chunk of stream) {
            if (
                chunk.type === "content_block_delta" &&
                chunk.delta.type === "text_delta"
            ) {
                webSearchContext += chunk.delta.text;
            }
        }

        if (webSearchContext.trim()) {
            elizaLogger.debug(
                "Web search completed successfully",
                `${systemPrompt}\n\n## ADDITIONAL WEB CONTEXT:\n${webSearchContext}`
            );
            return `${systemPrompt}\n\n## ADDITIONAL WEB CONTEXT:\n${webSearchContext}`;
        } else {
            elizaLogger.debug("Web search returned no results");
            return systemPrompt;
        }
    } catch (error) {
        elizaLogger.error("Error performing web search:", error);
        return systemPrompt;
    }
}

/**
 * Create router for tweet-related endpoints
 * @param agents Map of agent runtimes
 * @returns Express router
 */
export function createPostRoutes(
    agents: Map<string, AgentRuntime>
): express.Router {
    const router = express.Router();

    // Get tweets for an agent
    router.get("/agents/:agentId/posts", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }
        if (agent.clients["linkedin"]) {
            const linkedInClient = agent.clients["linkedin"];
            if (linkedInClient && linkedInClient.post.currentPlanId) {
                const postManager = linkedInClient.post;
                const contentManager = postManager.contentPlanManager;
                const plan = await contentManager.getPlan(
                    postManager.currentPlanId
                );
                plan.posts = plan.posts.sort(
                    (a: ScheduledPost, b: ScheduledPost) =>
                        new Date(a.scheduledTime).getTime() -
                        new Date(b.scheduledTime).getTime()
                );
                res.json({
                    id: agent.agentId,
                    plan: {
                        id: plan.id,
                        posts: plan.posts,
                        metadata: {
                            totalPosts: plan.posts.length,
                        },
                    },
                });
                return;
            }
        }
        if (agent.clients["twitter"]) {
            const twitterClient = agent.clients["twitter"];
            if (twitterClient && twitterClient.post.currentPlanId) {
                const postManager = twitterClient.post;
                const contentManager = postManager.contentPlanManager;
                const plan = await contentManager.getPlan(
                    postManager.currentPlanId
                );
                plan.posts = plan.posts.sort(
                    (a: ScheduledPost, b: ScheduledPost) =>
                        new Date(a.scheduledTime).getTime() -
                        new Date(b.scheduledTime).getTime()
                );
                res.json({
                    id: agent.agentId,
                    plan: {
                        id: plan.id,
                        posts: plan.posts,
                        metadata: {
                            totalPosts: plan.posts.length,
                        },
                    },
                });
                return;
            }
        }

        res.status(404).json({ error: "Could not find agent posts" });
    });

    // Upload canvas image endpoint
    router.post(
        "/agents/:agentId/upload-canvas-image",
        upload.single("image"),
        async (req: CustomRequest, res: express.Response) => {
            const agentId = req.params.agentId;
            const agent = agents.get(agentId);
            const planId = req.body.planId || "canvas-upload";

            if (!agent) {
                res.status(404).json({ error: "Agent not found" });
                return;
            }

            if (!req.file) {
                res.status(400).json({ error: "No image file provided" });
                return;
            }

            try {
                const uploadDir = path.dirname(req.file.path);
                if (!fs.existsSync(uploadDir)) {
                    fs.mkdirSync(uploadDir, { recursive: true });
                }
                const newFilename = `canvas_${planId}_${Date.now()}`;
                const newPath = path.join(uploadDir, newFilename);
                const finalPath =
                    newPath + "." + req.file.mimetype.split("/")[1];
                fs.renameSync(req.file.path, finalPath);
                const normalizedPath = finalPath.replace(/\\/g, "/");
                const filename = normalizedPath.split("/").pop();
                const filepath = `https://media-pilot.dreamstarter.xyz/media/uploads/${filename}`;

                res.json({
                    success: true,
                    filepath: filepath,
                    localPath: finalPath,
                });
            } catch (error) {
                console.error("Error uploading canvas image:", error);
                res.status(500).json({
                    success: false,
                    error: "Failed to upload canvas image",
                });
            }
        }
    );

    // Update post with attachments (combined endpoint)
    router.post(
        "/agents/:agentId/update-post",
        upload.single("image"),
        async (req: CustomRequest, res: express.Response) => {
            const agentId = req.params.agentId;
            const agent = agents.get(agentId);
            const postId = req.body.postId;
            const planId = req.body.planId;
            const content = req.body.content;
            const imageFromAI = req.body.imageFromAI;
            const scheduledTime = req.body.scheduledTime;
            let filepath = imageFromAI;
            let newPath: string | undefined;

            if (!agent) {
                res.status(404).json({ error: "Agent not found" });
                return;
            }

            // Process image if provided (either uploaded or from AI)
            if (imageFromAI || req.file) {
                // Handle AI-generated image
                if (imageFromAI) {
                    const baseDir = path.join(process.cwd(), "generatedImages");
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(baseDir)) {
                        fs.mkdirSync(baseDir, { recursive: true });
                    }
                    const generatedFileName = imageFromAI?.split("/").pop();
                    newPath = path.join(baseDir, `${generatedFileName}`);
                }
                // Handle uploaded image
                else if (req.file) {
                    const uploadDir = path.dirname(req.file.path);
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(uploadDir)) {
                        fs.mkdirSync(uploadDir, { recursive: true });
                    }
                    const newFilename = `${planId}_${Date.now()}`;
                    newPath = path.join(uploadDir, newFilename);
                    newPath = newPath + "." + req.file.mimetype.split("/")[1];
                    fs.renameSync(req.file.path, newPath);
                    const normalizedPath = newPath.replace(/\\/g, "/");
                    const filename = normalizedPath.split("/").pop();
                    filepath = `https://media-pilot.dreamstarter.xyz/media/uploads/${filename}`;
                }
            }

            try {
                const twitterClient = agent.clients["twitter"];
                const linkedInClient = agent.clients["linkedin"];

                // Prepare update object with all the fields that need to be updated
                const updateObject: {
                    content?: string;
                    scheduledTime?: Date;
                    attachments?: string[];
                    localPath?: Array<{
                        type: string;
                        url: string;
                    }>;
                } = {};

                // Add content if provided
                if (content !== undefined) {
                    updateObject.content = content;
                }

                // Add scheduled time if provided
                if (scheduledTime !== undefined) {
                    updateObject.scheduledTime = new Date(scheduledTime);
                }

                // Add attachments if image was provided
                if (filepath) {
                    updateObject.attachments = [filepath];
                    if (newPath) {
                        updateObject.localPath = [
                            {
                                type: imageFromAI
                                    ? "image/png"
                                    : req.file?.mimetype,
                                url: newPath,
                            },
                        ];
                    }
                }

                // If no image was provided but we're explicitly updating attachments (e.g., removing an image)
                if (filepath === null) {
                    updateObject.attachments = [];
                    updateObject.localPath = [];
                }

                // Update the post with all the provided fields
                if (linkedInClient && linkedInClient.post.currentPlanId) {
                    const postManager = linkedInClient.post;
                    const contentManager = postManager.contentPlanManager;
                    await contentManager.updatePost(
                        planId,
                        postId,
                        updateObject
                    );
                    res.json({
                        status: "success",
                    });
                    return;
                }

                if (twitterClient && twitterClient.post.currentPlanId) {
                    const postManager = twitterClient.post;
                    const contentManager = postManager.contentPlanManager;
                    await contentManager.updatePost(
                        planId,
                        postId,
                        updateObject
                    );
                    res.json({
                        status: "success",
                    });
                    return;
                }

                res.status(404).json({
                    status: "failed",
                    error: "No valid social media client found",
                });
            } catch (error) {
                console.error("Error updating post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error updating post",
                });
            }
        }
    );

    // Generate new tweets
    router.post("/agents/:agentId/new-tweets", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const planId = req.body.planId;
        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];
        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                await contentManager.refreshNewPosts(
                    planId,
                    postManager.postInterval
                );

                res.json({
                    status: "success",
                    message: "New Posts generated successfully",
                });
            } catch (error) {
                console.error("Error generating posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error generating posts",
                });
            }
            return;
        }
        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                await contentManager.refreshNewPosts(
                    planId,
                    postManager.postInterval
                );

                res.json({
                    status: "success",
                    message: "New Posts generated successfully",
                });
            } catch (error) {
                console.error("Error generating posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error generating posts",
                });
            }
            return;
        }

        res.status(404).json({
            status: "failed",
            error: "Twitter client or plan not found",
        });
    });

    // Remove tweet image
    router.delete("/agents/:agentId/remove-tweet-image", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const postId = req.body.postId;
        const planId = req.body.planId;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];
        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Update the post with empty attachments array to remove images
                await contentManager.updatePost(planId, postId, {
                    attachments: [],
                });

                res.json({
                    status: "success",
                    message: "Image removed successfully",
                });
            } catch (error) {
                console.error("Error removing image:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to remove image",
                });
            }
            return;
        }
        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Update the post with empty attachments array to remove images
                await contentManager.updatePost(planId, postId, {
                    attachments: [],
                });

                res.json({
                    status: "success",
                    message: "Image removed successfully",
                });
            } catch (error) {
                console.error("Error removing image:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to remove image",
                });
            }
            return;
        }

        res.status(404).json({
            status: "failed",
            error: "Twitter client or plan not found",
        });
    });

    // Delete a scheduled post
    router.delete("/agents/:agentId/delete-post", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const postId = req.body.postId;
        const planId = req.body.planId;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!postId || !planId) {
            res.status(400).json({ error: "Post ID and Plan ID are required" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];

        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Get the current plan
                const plan = await contentManager.getPlan(planId);
                if (!plan) {
                    res.status(404).json({
                        status: "failed",
                        error: "Content plan not found",
                    });
                    return;
                }

                // Find the post index
                const postIndex = plan.posts.findIndex(
                    (post: ScheduledPost) => post.id === postId
                );
                if (postIndex === -1) {
                    res.status(404).json({
                        status: "failed",
                        error: "Post not found in the plan",
                    });
                    return;
                }

                // Remove the post from the array
                plan.posts.splice(postIndex, 1);

                // Update the metadata
                plan.metadata.totalPosts = plan.posts.length;

                // Save the updated plan
                await contentManager.storePlan(plan);

                res.json({
                    status: "success",
                    message: "Post deleted successfully",
                });
                return;
            } catch (error) {
                console.error("Error deleting post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to delete post",
                });
                return;
            }
        }

        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Get the current plan
                const plan = await contentManager.getPlan(planId);
                if (!plan) {
                    res.status(404).json({
                        status: "failed",
                        error: "Content plan not found",
                    });
                    return;
                }

                // Find the post index
                const postIndex = plan.posts.findIndex(
                    (post: ScheduledPost) => post.id === postId
                );
                if (postIndex === -1) {
                    res.status(404).json({
                        status: "failed",
                        error: "Post not found in the plan",
                    });
                    return;
                }

                // Remove the post from the array
                plan.posts.splice(postIndex, 1);

                // Update the metadata
                plan.metadata.totalPosts = plan.posts.length;

                // Save the updated plan
                await contentManager.storePlan(plan);

                res.json({
                    status: "success",
                    message: "Post deleted successfully",
                });
                return;
            } catch (error) {
                console.error("Error deleting post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to delete post",
                });
                return;
            }
        }

        res.status(404).json({
            status: "failed",
            error: "No valid social media client found",
        });
    });

    // Schedule a post
    router.post(
        "/agents/:agentId/schedule-post",
        upload.single("image"),
        async (req: CustomRequest, res: express.Response) => {
            const agentId = req.params.agentId;
            const agent = agents.get(agentId);
            const { content, scheduledTime, platform, imageFromAI } = req.body;
            let filepath = imageFromAI;
            let newPath: string | undefined;

            if (!agent) {
                res.status(404).json({ error: "Agent not found" });
                return;
            }

            // Process image if provided (either uploaded or from AI)
            if (imageFromAI || req.file) {
                // Handle AI-generated image
                if (imageFromAI) {
                    const baseDir = path.join(process.cwd(), "generatedImages");
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(baseDir)) {
                        fs.mkdirSync(baseDir, { recursive: true });
                    }
                    const generatedFileName = imageFromAI?.split("/").pop();
                    newPath = path.join(baseDir, `${generatedFileName}`);
                }
                // Handle uploaded image
                else if (req.file) {
                    const uploadDir = path.dirname(req.file.path);
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(uploadDir)) {
                        fs.mkdirSync(uploadDir, { recursive: true });
                    }
                    const newFilename = `schedule_${Date.now()}`;
                    newPath = path.join(uploadDir, newFilename);
                    newPath = newPath + "." + req.file.mimetype.split("/")[1];
                    fs.renameSync(req.file.path, newPath);
                    const normalizedPath = newPath.replace(/\\/g, "/");
                    const filename = normalizedPath.split("/").pop();
                    filepath = `https://media-pilot.dreamstarter.xyz/media/uploads/${filename}`;
                }
            }

            try {
                const results = [];
                // Handle LinkedIn platform
                if (platform === "linkedin" && agent.clients["linkedin"]) {
                    const linkedInClient = agent.clients["linkedin"];
                    if (linkedInClient && linkedInClient.post.currentPlanId) {
                        const postManager = linkedInClient.post;
                        const contentManager = postManager.contentPlanManager;

                        // Create new post object with optional attachments
                        const newPost: ScheduledPost = {
                            id: `post-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                            content: content,
                            scheduledTime: new Date(scheduledTime),
                            status: "approved",
                            topics: [], // You might want to extract topics from content
                        };

                        // Add attachment if image was provided
                        if (filepath) {
                            newPost.attachments = [filepath];
                            if (newPath) {
                                newPost.localPath = [
                                    {
                                        type: imageFromAI
                                            ? "image/png"
                                            : req.file?.mimetype,
                                        url: newPath,
                                    },
                                ];
                            }
                        }

                        const plan = await contentManager.getPlan(
                            postManager.currentPlanId
                        );
                        if (!plan) {
                            results.push({
                                platform,
                                status: "failed",
                                error: "Content plan not found",
                            });
                        }

                        plan.posts.push(newPost);
                        plan.metadata.totalPosts = plan.posts.length;

                        plan.posts = plan.posts.sort(
                            (a: ScheduledPost, b: ScheduledPost) =>
                                new Date(a.scheduledTime).getTime() -
                                new Date(b.scheduledTime).getTime()
                        );

                        await contentManager.storePlan(plan);

                        results.push({
                            platform,
                            status: "success",
                            post: newPost,
                        });
                    }
                }
                // Handle Twitter platform
                else if (platform === "twitter" && agent.clients["twitter"]) {
                    const twitterClient = agent.clients["twitter"];
                    if (twitterClient && twitterClient.post.currentPlanId) {
                        const postManager = twitterClient.post;
                        const contentManager = postManager.contentPlanManager;

                        // Create new post object with optional attachments
                        const newPost: ScheduledPost = {
                            id: `post-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                            content: content,
                            scheduledTime: new Date(scheduledTime),
                            status: "approved",
                            topics: [], // You might want to extract topics from content
                        };

                        // Add attachment if image was provided
                        if (filepath) {
                            newPost.attachments = [filepath];
                            if (newPath) {
                                newPost.localPath = [
                                    {
                                        type: imageFromAI
                                            ? "image/png"
                                            : req.file?.mimetype,
                                        url: newPath,
                                    },
                                ];
                            }
                        }

                        const plan = await contentManager.getPlan(
                            postManager.currentPlanId
                        );
                        if (!plan) {
                            results.push({
                                platform,
                                status: "failed",
                                error: "Content plan not found",
                            });
                        }

                        plan.posts.push(newPost);
                        plan.metadata.totalPosts = plan.posts.length;

                        plan.posts = plan.posts.sort(
                            (a: ScheduledPost, b: ScheduledPost) =>
                                new Date(a.scheduledTime).getTime() -
                                new Date(b.scheduledTime).getTime()
                        );

                        await contentManager.storePlan(plan);

                        results.push({
                            platform,
                            status: "success",
                            post: newPost,
                        });
                    }
                }
                if (results.length === 0) {
                    res.status(400).json({
                        status: "failed",
                        error: "No valid platforms found or missing client configurations",
                    });
                    return;
                }
                res.json({
                    status: "success",
                    results,
                });
            } catch (error) {
                console.error("Error scheduling posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error scheduling posts",
                });
            }
        }
    );

    // Improve a post using a prompt
    router.post("/agents/:agentId/improve-post-content", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const { systemPrompt, context } = req.body;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!systemPrompt) {
            res.status(400).json({ error: "System prompt is required" });
            return;
        }

        // Default context if not provided
        const postContext = context || "";

        // Determine model size (SMALL, MEDIUM, LARGE)
        const modelClass = ModelClass.LARGE;

        try {
            const isTwitterAgent = !!agent.clients["twitter"];
            const selectedTemplate = isTwitterAgent
                ? copyImprovementTwitterTemplate
                : copyImprovementTemplate;

            // Generate post content using the enhanced system prompt
            const postContent = await generateText({
                runtime: agent,
                context: selectedTemplate.replace(
                    "%originalCopy%",
                    postContext
                ),
                modelClass: modelClass,
                customSystemPrompt: systemPrompt,
            });

            // Clean up the response to ensure it's properly formatted
            const cleanedContent = postContent
                .trim()
                .replace(/^```json\s*|\s*```$/g, "") // Remove markdown code blocks if present
                .replace(/^['"](.*)['"]$/g, "$1") // Remove outer quotes if present
                .replace(/\\n/g, "\n") // Replace escaped newlines
                .replace(/\\"/g, '"'); // Replace escaped quotes

            res.json({
                status: "success",
                post: {
                    content: cleanedContent,
                },
            });
        } catch (error) {
            console.error("Error generating post:", error);
            res.status(500).json({
                status: "failed",
                error: "Error generating post",
            });
        }
    });

    // Generate Post
    router.post("/agents/:agentId/generate-post-content", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const { systemPrompt, isKnowledgeBase, isSearchEnabled } = req.body;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!systemPrompt) {
            res.status(400).json({ error: "System prompt is required" });
            return;
        }

        const modelClass = ModelClass.LARGE;

        try {
            // Get project ID from the current agent
            const projectId = agent.character.projectId;

            if (!projectId) {
                res.status(400).json({
                    error: "Agent does not have a project ID",
                });
                return;
            }

            // Find all agents with the same project ID
            const projectAgents = Array.from(agents.values()).filter(
                (a) => a.character.projectId === projectId
            );

            if (projectAgents.length === 0) {
                res.status(404).json({
                    error: "No agents found for this project",
                });
                return;
            }

            // Perform web search to enhance the system prompt with current information
            let enhancedSystemPrompt = `${systemPrompt}`;
            if (isSearchEnabled) {
                enhancedSystemPrompt = `${systemPrompt} along with web information: ${await performWebSearchForContent(
                    systemPrompt,
                    agent
                )}`;
                await new Promise((resolve) => setTimeout(resolve, 10000));
            }

            // Generate content for all agents/platforms with the same project ID
            const results = [];

            for (const currentAgent of projectAgents) {
                try {
                    const state = await currentAgent.composeState({
                        userId: currentAgent.agentId,
                        roomId: currentAgent.agentId,
                        agentId: currentAgent.agentId,
                        content: {
                            text: enhancedSystemPrompt,
                        },
                    });

                    const isTwitterAgent = !!currentAgent.clients["twitter"];
                    const isLinkedInAgent = !!currentAgent.clients["linkedin"];
                    const isInstagramAgent =
                        !!currentAgent.clients["instagram"];

                    let selectedTemplate: string;
                    let platform: string;

                    if (isTwitterAgent) {
                        platform = "twitter";
                        // Check if Twitter account has premium status
                        const twitterClient = currentAgent.clients["twitter"];
                        let isPremium = false;

                        try {
                            if (twitterClient && twitterClient.client) {
                                isPremium =
                                    await twitterClient.client.hasPremiumSubscription();
                            }
                        } catch (error) {
                            elizaLogger.warn(
                                "Error checking premium status, defaulting to standard:",
                                error
                            );
                            isPremium = false;
                        }

                        if (isPremium) {
                            selectedTemplate = isKnowledgeBase
                                ? twitterPostPremiumTemplate
                                : twitterPostPremiumWithoutKnowledgeBaseTemplate;
                        } else {
                            selectedTemplate = isKnowledgeBase
                                ? twitterPostTemplate
                                : twitterPostWithoutKnowledgeBaseTemplate;
                        }
                    } else if (isLinkedInAgent) {
                        platform = "linkedin";
                        selectedTemplate = isKnowledgeBase
                            ? linkedInPostTemplate
                            : linkedInPostWithoutKnowledgeBaseTemplate;
                    } else if (isInstagramAgent) {
                        platform = "instagram";
                        // Use LinkedIn template as fallback for Instagram
                        selectedTemplate = isKnowledgeBase
                            ? linkedInPostTemplate
                            : linkedInPostWithoutKnowledgeBaseTemplate;
                    } else {
                        // Skip agents without supported clients
                        continue;
                    }

                    const context = composeContext({
                        state,
                        template: selectedTemplate,
                    });

                    console.log(
                        `Generating content for ${platform} agent:`,
                        currentAgent.agentId
                    );

                    // Generate post content using the enhanced system prompt
                    const postContent = await generateText({
                        runtime: currentAgent,
                        context,
                        modelClass: modelClass,
                        customSystemPrompt: `Generate a social media post on this topic: ${enhancedSystemPrompt}`,
                    });

                    // Clean up the response to ensure it's properly formatted
                    const cleanedContent = postContent
                        .trim()
                        .replace(/^```json\s*|\s*```$/g, "") // Remove markdown code blocks if present
                        .replace(/^['"](.*)['"]$/g, "$1") // Remove outer quotes if present
                        .replace(/\\n/g, "\n") // Replace escaped newlines
                        .replace(/\\"/g, '"'); // Replace escaped quotes

                    results.push({
                        agentId: currentAgent.agentId,
                        platform: platform,
                        content: cleanedContent,
                    });
                } catch (error) {
                    console.error(
                        `Error generating content for agent ${currentAgent.agentId}:`,
                        error
                    );
                }
            }

            res.json({
                status: "success",
                projectId: projectId,
                posts: results,
            });
        } catch (error) {
            console.error("Error generating posts:", error);
            res.status(500).json({
                status: "failed",
                error: "Error generating posts",
            });
        }
    });

    return router;
}
